'use client'
import React, { useEffect, useRef, useState, useCallback } from 'react'

import ReactQuill, { Quill } from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import QuillResizeImage from 'quill-resize-image'

import { useS3ImageUpload } from '@/hooks/useS3ImageUpload'

interface EditorProps {
  value?: string
  onChange?: (value: string) => void
  onEditorRef?: (instance: any) => void
  containerClassName?: string
  className?: string
  placeholder?: string
  optionalToolbar?: boolean
  // S3 Upload Props
  onFile?: string
  onImageUpload?: (url: string) => void
  maxImageSize?: number
  allowedImageTypes?: string[]
}

const Editor = ({
  value,
  onChange,
  onEditorRef,
  containerClassName = '',
  className = '',
  placeholder = '',
  optionalToolbar = false,
  onFile = 'editor-uploads',
  onImageUpload,
  maxImageSize = 5 * 1024 * 1024, // 5MB default
  allowedImageTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp']
}: EditorProps) => {
  const quillRef = useRef<ReactQuill>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  // S3 Upload hook
  const { uploadImage, isLoading: s3Loading } = useS3ImageUpload({ onFile })

  const icons = ReactQuill.Quill.import('ui/icons')

  icons['code-block'] =
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><polyline points="64 32 32 64 64 96" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><polyline points="104 32 136 64 104 96" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><path d="M176,48h24a8,8,0,0,1,8,8V200a8,8,0,0,1-8,8H56a8,8,0,0,1-8-8V136" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/></svg>'

  // Custom image handler for S3 upload
  const imageHandler = useCallback(() => {
    const input = document.createElement('input')

    input.setAttribute('type', 'file')
    input.setAttribute('accept', allowedImageTypes.join(','))
    input.click()

    input.onchange = async () => {
      const file = input.files?.[0]

      if (!file) return

      // Validate file size
      if (file.size > maxImageSize) {
        alert(`File too large. Maximum size is ${Math.round(maxImageSize / (1024 * 1024))}MB`)

        return
      }

      // Validate file type
      if (!allowedImageTypes.includes(file.type)) {
        alert(`Invalid file type. Allowed types: ${allowedImageTypes.join(', ')}`)

        return
      }

      const quill = quillRef.current?.getEditor()

      if (!quill) return

      const range = quill.getSelection(true)

      setIsUploading(true)
      setUploadProgress(0)

      try {
        // Insert placeholder while uploading
        quill.insertEmbed(range.index, 'image', '/images/loading-placeholder.gif')
        quill.setSelection({ index: range.index + 1, length: 0 })

        // Upload to S3
        const imageUrl = await uploadImage(file)

        // Replace placeholder with actual image
        quill.deleteText(range.index, 1)
        quill.insertEmbed(range.index, 'image', imageUrl)
        quill.setSelection({ index: range.index + 1, length: 0 })

        // Call callback if provided
        if (onImageUpload) {
          onImageUpload(imageUrl)
        }

        setUploadProgress(100)
      } catch (error) {
        console.error('Image upload failed:', error)

        // Remove placeholder on error
        quill.deleteText(range.index, 1)

        // Show error message
        const errorMessage = error instanceof Error ? error.message : 'Image upload failed'

        alert(`Upload failed: ${errorMessage}`)
      } finally {
        setIsUploading(false)
        setTimeout(() => setUploadProgress(0), 1000)
      }
    }
  }, [uploadImage, onImageUpload, maxImageSize, allowedImageTypes])

  // useEffect(() => {
  //   if (quillRef.current && onEditorRef) {
  //     onEditorRef(quillRef.current)
  //   }
  // }, [onEditorRef])

  const [isEditorVisible, setIsEditorVisible] = useState(optionalToolbar ? false : true)

  if (typeof window !== 'undefined') {
    Quill.register('modules/resize', QuillResizeImage)
  }

  useEffect(() => {
    if (quillRef.current) {
      const container = quillRef.current.editor?.root?.parentElement

      if (container) {
        container.className = `${container.className} ${containerClassName}`
      }
    }
  }, [quillRef, containerClassName])

  // Configure modules with custom image handler
  const modules = {
    toolbar: {
      container: [
        [{ header: [2, 3, 4, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link', 'image', 'code', 'code-block']
      ],
      handlers: {
        image: imageHandler
      }
    },
    resize: {
      locale: {}
    },
  }

  const formats = ['header', 'bold', 'italic', 'underline', 'list', 'bullet', 'link', 'image', 'code', 'code-block']

  return (
    <div
      className={`custom-quill-editor ${className} ${optionalToolbar && !isEditorVisible ? 'optional-toolbar' : ''}`}
    >
      {/* Upload Progress Indicator */}
      {(isUploading || s3Loading) && (
        <div className="upload-progress-container">
          <div className="upload-progress-bar">
            <div
              className="upload-progress-fill"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <span className="upload-progress-text">
            {uploadProgress > 0 ? `Uploading... ${uploadProgress}%` : 'Preparing upload...'}
          </span>
        </div>
      )}

      <ReactQuill
        placeholder={placeholder}
        ref={quillRef}
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        theme='snow'
        onFocus={() => setIsEditorVisible(true)}
        onBlur={() => setIsEditorVisible(false)}
      />
    </div>
  )
}

export default Editor
