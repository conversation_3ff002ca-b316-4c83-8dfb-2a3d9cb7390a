.quill-editor-image-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 1px dashed #ccc;
  border-radius: 4px;
  background-color: #f8f8f8;
  cursor: pointer;
  margin-bottom: 1rem;
}

.quill-editor-image-uploader:hover {
  background-color: #f0f0f0;
}

.quill-editor-image-uploader p {
  margin: 0.5rem 0;
  color: #666;
}

.quill-editor-image-uploading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.quill-editor-image-uploading p {
  margin-left: 0.5rem;
}

.custom-quill-editor .ql-editor {
  min-height: 200px;
}

.custom-quill-editor.optional-toolbar .ql-toolbar {
  display: none;
}

/* Image URL input styling */
.image-url-input-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.image-url-input-row {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.image-url-input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.image-url-button,
.image-url-cancel,
.get-presigned-url-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  white-space: nowrap;
}

.image-url-button {
  background-color: #4075ff;
  color: white;
}

.image-url-button:disabled {
  background-color: #a0b6eb;
  cursor: not-allowed;
}

.get-presigned-url-button {
  background-color: #34c759;
  color: white;
}

.get-presigned-url-button:disabled {
  background-color: #a0d9b4;
  cursor: wait;
}

.image-url-cancel {
  background-color: #f1f1f1;
  color: #333;
}

.presigned-url-info {
  margin-top: 10px;
  padding: 8px;
  background-color: #e8f4fd;
  border-radius: 4px;
  border-left: 4px solid #4075ff;
}

.presigned-url-note {
  margin: 0;
  font-size: 13px;
  color: #333;
}

/* Image resize handles styling */
.custom-quill-editor .image-resize-handles .resize-handle {
  display: block;
  position: absolute;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background-color: #4075ff;
  border: 1px solid #fff;
}

.custom-quill-editor .image-resize-handles .resize-handle.se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.custom-quill-editor .image-resize-handles .resize-handle.sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.custom-quill-editor .image-resize-handles .resize-handle.ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.custom-quill-editor .image-resize-handles .resize-handle.nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

/* Upload Progress Styles */
.upload-progress-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 12px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.upload-progress-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.upload-progress-fill {
  height: 100%;
  background-color: #4075ff;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.upload-progress-text {
  color: #666;
  font-size: 12px;
  white-space: nowrap;
}

/* Adjust editor container for progress bar */
.custom-quill-editor {
  position: relative;
}

.custom-quill-editor .ql-container {
  transition: margin-top 0.3s ease;
}

.custom-quill-editor:has(.upload-progress-container) .ql-container {
  margin-top: 40px;
}
