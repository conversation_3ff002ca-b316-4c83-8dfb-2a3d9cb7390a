'use client'
import type { ReactElement } from 'react'
import React, { useState } from 'react'

import dynamic from 'next/dynamic'

// MUI Imports
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material'

// Form Imports
import { Controller, useForm, FormProvider } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'

// Layout Import
import Layout from '@/layouts/MainLayout'

// Dynamic Editor Import (following project pattern)
const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false,
  loading: () => (
    <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
      <CircularProgress color="primary" />
    </Box>
  )
})

// Form validation schema
const demoFormSchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  content: yup
    .string()
    .required('Content is required')
    .test('not-empty', 'Content cannot be empty', value => {
      if (!value) return false

      const stripHtml = value.replace(/<[^>]*>/g, '').trim()

      return stripHtml !== '' && value !== '<p><br></p>'
    }),
  description: yup.string().optional()
})

interface DemoFormData {
  title: string
  content: string
  description?: string
}

const EditorDemo = () => {
  // Basic editor state
  const [basicContent, setBasicContent] = useState('<p>Welcome to the Editor demo! Try typing here...</p>')
  const [placeholderContent, setPlaceholderContent] = useState('')
  const [optionalToolbarContent, setOptionalToolbarContent] = useState('')
  
  // Editor configuration states
  const [showOptionalToolbar, setShowOptionalToolbar] = useState(false)
  const [editorRef, setEditorRef] = useState<any>(null)
  
  // Form setup
  const methods = useForm<DemoFormData>({
    resolver: yupResolver(demoFormSchema),
    defaultValues: {
      title: '',
      content: '',
      description: ''
    }
  })

  const { control, handleSubmit, formState: { errors }, reset } = methods

  // Form submission handler
  const onSubmit = (data: DemoFormData) => {
    console.log('Form submitted:', data)
    alert('Form submitted successfully! Check the console for details.')
  }

  // Reset form handler
  const handleReset = () => {
    reset()
    setBasicContent('<p>Welcome to the Editor demo! Try typing here...</p>')
    setPlaceholderContent('')
    setOptionalToolbarContent('')
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Editor Component Demo
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        This demo showcases the various features and usage patterns of the Editor component.
        The Editor is built on top of ReactQuill and includes image resizing, custom toolbar, and form integration.
      </Typography>

      <Grid container spacing={4}>
        {/* Basic Usage */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="1. Basic Usage" 
              subheader="Simple editor with default configuration"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                This demonstrates the most basic usage of the Editor component with controlled state.
              </Typography>
              
              <Editor
                value={basicContent}
                onChange={setBasicContent}
                placeholder="Start typing your content here..."
              />
              
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Content length: {basicContent.replace(/<[^>]*>/g, '').length} characters
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Placeholder Demo */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="2. Custom Placeholder" 
              subheader="Editor with custom placeholder text"
            />
            <CardContent>
              <Editor
                value={placeholderContent}
                onChange={setPlaceholderContent}
                placeholder="Enter your article content here. You can use formatting, add images, and create lists..."
                className="min-h-[150px]"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Optional Toolbar Demo */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="3. Optional Toolbar"
              subheader="Toolbar appears only when focused"
            />
            <CardContent>
              <FormControlLabel
                control={
                  <Switch
                    checked={showOptionalToolbar}
                    onChange={(e) => setShowOptionalToolbar(e.target.checked)}
                  />
                }
                label="Enable optional toolbar mode"
              />

              <Box mt={2}>
                <Editor
                  value={optionalToolbarContent}
                  onChange={setOptionalToolbarContent}
                  placeholder="Click here to start editing. Toolbar will appear on focus."
                  optionalToolbar={showOptionalToolbar}
                  className="min-h-[150px]"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Form Integration Demo */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="4. Form Integration with Validation"
              subheader="Editor integrated with React Hook Form and validation"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                This demonstrates how to integrate the Editor with React Hook Form, including validation patterns used throughout the project.
              </Typography>

              <FormProvider {...methods}>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="title"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            fullWidth
                            label="Title"
                            error={!!errors.title}
                            helperText={errors.title?.message}
                            placeholder="Enter a title for your content"
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Main Content *
                      </Typography>
                      <Controller
                        name="content"
                        control={control}
                        render={({ field: { ref, ...field } }) => (
                          <Editor
                            {...field}
                            onEditorRef={(instance) => {
                              ref(instance)
                              setEditorRef(instance)
                            }}
                            placeholder="Write your main content here. This field is required and validates that content is not empty."
                            className="min-h-[200px]"
                            onFile="demo/form-content"
                            onImageUpload={(url) => {
                              console.log('Form content image uploaded:', url)
                            }}
                          />
                        )}
                      />
                      {errors.content && (
                        <Typography variant="caption" color="error" display="block" mt={1}>
                          {errors.content.message}
                        </Typography>
                      )}
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Description (Optional)
                      </Typography>
                      <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                          <Editor
                            {...field}
                            placeholder="Add an optional description or summary..."
                            optionalToolbar
                            className="min-h-[120px]"
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Box display="flex" gap={2}>
                        <Button type="submit" variant="contained" color="primary">
                          Submit Form
                        </Button>
                        <Button type="button" variant="outlined" onClick={handleReset}>
                          Reset All
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </form>
              </FormProvider>
            </CardContent>
          </Card>
        </Grid>

        {/* Custom Styling Demo */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="5. Custom Styling"
              subheader="Different className and containerClassName examples"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Custom styling with className and containerClassName props.
              </Typography>

              <Editor
                value=""
                onChange={() => {}}
                placeholder="This editor has custom styling applied..."
                className="border-2 border-blue-200 rounded-lg"
                containerClassName="bg-blue-50"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* S3 Image Upload Demo */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="6. S3 Image Upload"
              subheader="Custom image upload functionality with AWS S3 integration"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                This demonstrates the S3 image upload functionality. Click the image button in the toolbar
                to upload images directly to AWS S3. The editor shows upload progress and handles errors gracefully.
              </Typography>

              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>S3 Upload Features:</strong> Automatic file validation, progress tracking,
                  error handling, and CloudFront URL conversion. Supports PNG, JPG, JPEG, GIF, and WebP formats up to 5MB.
                </Typography>
              </Alert>

              <Editor
                value=""
                onChange={() => {}}
                placeholder="Try uploading an image using the image button in the toolbar above..."
                onFile="demo/images"
                onImageUpload={(url) => {
                  console.log('Image uploaded:', url)
                }}
                className="min-h-[200px]"
              />

              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Images are uploaded to the &quot;demo/images&quot; folder in S3. Check the browser console to see upload callbacks.
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Editor Reference Demo */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="7. Editor Reference"
              subheader="Accessing editor instance for advanced operations"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Use onEditorRef to access the editor instance for advanced operations.
              </Typography>

              <Box mb={2}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    if (editorRef?.getEditor) {
                      const text = editorRef.getEditor().getText()

                      alert(`Current text content: &quot;${text.trim()}&quot;`)
                    }
                  }}
                  disabled={!editorRef}
                >
                  Get Text Content
                </Button>
              </Box>

              <Typography variant="caption" color="text.secondary" display="block" mb={1}>
                This editor reference is shared with the form content editor above.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Advanced Configuration Demo */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="8. Advanced S3 Configuration"
              subheader="Custom upload settings and callbacks"
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure custom upload settings including file size limits and allowed types.
              </Typography>

              <Editor
                value=""
                onChange={() => {}}
                placeholder="This editor has custom upload settings: 2MB limit, PNG/JPG only..."
                onFile="demo/restricted"
                maxImageSize={2 * 1024 * 1024} // 2MB
                allowedImageTypes={['image/png', 'image/jpeg']}
                onImageUpload={(url) => {
                  alert(`Custom upload callback: ${url}`)
                }}
                className="min-h-[150px]"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Features Overview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Editor Features Overview"
              subheader="Complete list of available features and toolbar options"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography variant="h6" gutterBottom>
                    Toolbar Features:
                  </Typography>
                  <ul>
                    <li>Headers (H2, H3, H4)</li>
                    <li>Text formatting (Bold, Italic, Underline)</li>
                    <li>Lists (Ordered and Bullet)</li>
                    <li>Links and Images</li>
                    <li>Code and Code blocks</li>
                    <li>Image resizing (with QuillResizeImage)</li>
                    <li><strong>S3 Image Upload</strong> (NEW)</li>
                  </ul>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="h6" gutterBottom>
                    Core Props:
                  </Typography>
                  <ul>
                    <li><code>value</code>: Controlled content value</li>
                    <li><code>onChange</code>: Content change handler</li>
                    <li><code>onEditorRef</code>: Editor instance callback</li>
                    <li><code>placeholder</code>: Placeholder text</li>
                    <li><code>className</code>: Custom CSS classes</li>
                    <li><code>containerClassName</code>: Container CSS classes</li>
                    <li><code>optionalToolbar</code>: Hide toolbar until focus</li>
                  </ul>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="h6" gutterBottom>
                    S3 Upload Props:
                  </Typography>
                  <ul>
                    <li><code>onFile</code>: S3 upload folder path</li>
                    <li><code>onImageUpload</code>: Upload success callback</li>
                    <li><code>maxImageSize</code>: File size limit (bytes)</li>
                    <li><code>allowedImageTypes</code>: Supported formats</li>
                  </ul>

                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    <strong>Default Settings:</strong><br/>
                    • Max size: 5MB<br/>
                    • Types: PNG, JPG, JPEG, GIF, WebP<br/>
                    • Progress tracking included<br/>
                    • Error handling built-in
                  </Typography>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Integration Tips:</strong> The Editor component is dynamically imported to avoid SSR issues.
                  It integrates seamlessly with React Hook Form and supports custom validation patterns.
                  Use the onEditorRef prop to access advanced Quill editor methods when needed.
                </Typography>
              </Alert>

              <Alert severity="success" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>S3 Upload Integration:</strong> The new S3 upload functionality replaces the default Quill image handler
                  with a robust AWS S3 upload system. Images are automatically validated, uploaded with progress tracking,
                  and converted to CloudFront URLs for optimal performance. The upload process is fully integrated with
                  the existing useS3ImageUpload hook and follows the project&apos;s established patterns.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

// Layout configuration (following project pattern)
EditorDemo.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default EditorDemo
