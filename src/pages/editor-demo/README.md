# Editor Component Demo

This demo page showcases the comprehensive usage of the Editor component (`src/components/Editor.tsx`) used throughout the online learning platform.

## Demo URL

Visit `/editor-demo` to see the live demonstration.

## What's Demonstrated

### 1. Basic Usage
- Simple controlled editor with default configuration
- Basic state management with `useState`
- Character count display

### 2. Custom Placeholder
- Custom placeholder text for user guidance
- Responsive layout demonstration

### 3. Optional Toolbar
- Dynamic toolbar visibility based on focus
- Toggle switch to enable/disable optional toolbar mode
- Demonstrates the `optionalToolbar` prop functionality

### 4. Form Integration with Validation
- Complete React Hook Form integration
- Yup validation schema implementation
- Error handling and display
- Form submission and reset functionality
- Multiple editors in a single form

### 5. Custom Styling
- Custom CSS classes with `className` prop
- Container styling with `containerClassName` prop
- Visual styling examples

### 6. S3 Image Upload
- Custom AWS S3 image upload functionality
- Upload progress tracking and error handling
- File validation (size, type)
- CloudFront URL conversion
- Integration with existing S3 upload system

### 7. Editor Reference
- Accessing the editor instance via `onEditorRef`
- Programmatic content manipulation
- Advanced editor operations

### 8. Advanced S3 Configuration
- Custom upload settings and file restrictions
- Upload callbacks and event handling
- Folder organization in S3

### 9. Features Overview
- Complete documentation of toolbar features
- Props reference guide (including new S3 props)
- Integration tips and best practices

## Key Features Showcased

### Editor Capabilities
- **Rich Text Formatting**: Bold, italic, underline
- **Headers**: H2, H3, H4 support
- **Lists**: Ordered and bullet lists
- **Media**: Image insertion and resizing
- **Code**: Inline code and code blocks
- **Links**: URL link insertion
- **S3 Image Upload**: Direct AWS S3 upload with progress tracking

### Technical Features
- **Dynamic Import**: SSR-safe component loading
- **Form Integration**: React Hook Form compatibility
- **Validation**: Custom validation patterns
- **TypeScript**: Full type safety
- **Responsive**: Mobile-friendly design
- **S3 Integration**: AWS S3 upload with CloudFront URLs
- **Upload Progress**: Real-time upload progress tracking
- **Error Handling**: Comprehensive upload error management

## Usage Patterns

### Basic Implementation
```tsx
import dynamic from 'next/dynamic'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false
})

const [content, setContent] = useState('')

<Editor
  value={content}
  onChange={setContent}
  placeholder="Start typing..."
/>
```

### Form Integration
```tsx
<Controller
  name="content"
  control={control}
  rules={{
    required: 'Content is required',
    validate: value => value !== '<p><br></p>' || 'Content cannot be empty'
  }}
  render={({ field: { ref, ...field } }) => (
    <Editor
      {...field}
      onEditorRef={ref}
      placeholder="Enter content..."
    />
  )}
/>
```

### Optional Toolbar
```tsx
<Editor
  value={content}
  onChange={setContent}
  optionalToolbar={true}
  placeholder="Click to start editing..."
/>
```

### S3 Image Upload
```tsx
<Editor
  value={content}
  onChange={setContent}
  onFile="courses/123"
  onImageUpload={(url) => console.log('Uploaded:', url)}
  maxImageSize={5 * 1024 * 1024} // 5MB
  allowedImageTypes={['image/png', 'image/jpeg', 'image/gif']}
  placeholder="Upload images directly to S3..."
/>
```

### Advanced S3 Configuration
```tsx
<Editor
  value={content}
  onChange={setContent}
  onFile="custom-folder/subfolder"
  maxImageSize={2 * 1024 * 1024} // 2MB limit
  allowedImageTypes={['image/png', 'image/jpeg']} // PNG/JPG only
  onImageUpload={(url) => {
    // Custom upload callback
    console.log('Image uploaded to:', url)
    // Update form state, show notification, etc.
  }}
/>
```

## Dependencies

The Editor component relies on:
- `react-quill`: Core rich text editor
- `quill-resize-image`: Image resizing functionality
- `react-hook-form`: Form integration
- `@mui/material`: UI components for the demo
- `useS3ImageUpload`: Custom hook for S3 upload functionality
- AWS S3 integration via presigned URLs

## Project Integration

This demo follows the project's established patterns:
- **Layout System**: Uses `MainLayout` with `getLayout` pattern
- **Styling**: Material-UI components and Tailwind CSS
- **TypeScript**: Full type safety and interfaces
- **Form Handling**: React Hook Form with Yup validation
- **Dynamic Imports**: SSR-safe component loading

## Development Notes

- The Editor component is client-side only (`'use client'`)
- Dynamic import prevents SSR hydration issues
- Form validation includes HTML content stripping
- Image resizing requires the `quill-resize-image` module
- Custom icons are defined for the code-block feature

## Best Practices Demonstrated

1. **Always use dynamic imports** for the Editor component
2. **Include loading states** during dynamic import
3. **Validate HTML content** by stripping tags for empty checks
4. **Use onEditorRef** for advanced editor operations
5. **Implement proper error handling** in forms
6. **Follow consistent naming conventions** for props and handlers
7. **Configure S3 upload paths** using the `onFile` prop for organization
8. **Set appropriate file size limits** based on your use case
9. **Validate file types** to prevent unsupported uploads
10. **Handle upload callbacks** for custom post-upload logic
11. **Provide user feedback** during upload progress
12. **Use CloudFront URLs** for optimal image delivery performance
